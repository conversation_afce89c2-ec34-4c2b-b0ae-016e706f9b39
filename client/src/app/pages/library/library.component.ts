import {Component, inject, PLATFORM_ID, signal, DestroyRef, effect, ViewChild, ElementRef, HostListener} from '@angular/core';
import {CommonModule, isPlatformBrowser, JsonPipe, NgOptimizedImage} from "@angular/common";
import {LibraryService} from "@/services/library.service";
import {<PERSON><PERSON>anitizer, Meta, Title} from "@angular/platform-browser";
import {ActivatedRoute, Router} from "@angular/router";
import {environment} from "@/env/environment";
import {NavItem} from 'epubjs'
import Epub from "epubjs";
import {ProfileService} from "@/services/profile.service";
import {ShareDataService} from "@/services/share-data.service";
import { IsInFavoritesPipe } from '@/pipes/isInFavorites.pipe';
import { ToasterService } from '@/services/toaster.service';
import {AdvertisingService} from "@/services/advertising.service";
import {TranslocoModule, TranslocoService} from "@jsverse/transloco";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthService } from '@/services/auth.service';
import {FormsModule} from "@angular/forms";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { TextInteractionComponent } from '../content/text-interaction/text-interaction.component';
import { Track } from '@/interfaces/track';
import { ReadingTimePipe } from '@/pipes/reading-time.pipe';
import { ClickOutsideDirective } from '@/directives/clickOutside';
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component';

@Component({
  selector: 'app-library',
  standalone: true,
  imports: [
    JsonPipe,
    CommonModule,
    IsInFavoritesPipe,
    FormsModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    TextInteractionComponent,
    TranslocoModule,
    ReadingTimePipe,
    ClickOutsideDirective,
    LoadingIndicatorComponent
  ],
  templateUrl: './library.component.html',
  styleUrl: './library.component.scss'
})
export class LibraryComponent {
  shareDataService = inject(ShareDataService)
  profileService = inject(ProfileService);
  libraryService = inject(LibraryService);
  authService = inject(AuthService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  platformId = inject(PLATFORM_ID)
  titleService = inject(Title)
  metaService = inject(Meta);
  advertisingService = inject(AdvertisingService);
  translocoService = inject(TranslocoService);
  auth = inject(AuthService)
  sanitizer = inject(DomSanitizer)
  code = this.route.snapshot.paramMap.get('code');
  selection: any = signal(null)
  toasterService = inject(ToasterService);
  quote: string | null = null
  id: number | null = null
  rendition: any = null
  data: any = null
  currentPage: any
  tabs = ['О книге', 'Читать', 'Аудиокнига']
  tab = 'О книге'
  descriptionTab = 'annotation'
  bookLoaded = false;
  bookId = ""
  private readonly destroyRef = inject(DestroyRef);
  chapter = 0
  theme = 'light'
  fontSize = '17'
  fontFamily = 'Arial'
  audioBookIndex = -1;
  chapterContent: any = ''
  chapterCount: number = 0
  chapterTitles: any = []
  chapterReadingTime: number = 0

  likesContent: any = [];
  favouriteContent: any = [];
  likesCount: number = 0;
  lang = 'ru';
  relatedBooksShowLast: boolean = false;
  relatedBooks = [
    {
      name: 'Панчадаши',
      author: 'Шри Видьяранья Свами',
      bookImgSrc: 'assets/images/book_1.webp'
    },
    {
      name: 'Аватары Господа Шивы',
      author: 'Всемирная Община Санатана Дхармы',
      bookImgSrc: 'assets/images/book_2.webp'
    },
    {
      name: 'Сборник лекций по Шайва-сиддханте',
      author: 'Шри Гуру Свами Вишнудевананда Гири',
      bookImgSrc: 'assets/images/book_3.webp'
    },
  ];
  fullscreenBookTheme = 'light';
  fullscreenBookFont = 'Prata';
  fullscreenBookFontSize: number = 24; // Will be set to responsive default in constructor
  fullscreenBookTextWidth = 'narrow'; // Default to narrow column (700px)
  isFullscreenBookDialogActive: boolean = false;
  showFullscreenOptions: boolean = false;
  similar: any = []
  contents: boolean = false;
  fullscreenContents: boolean = false;
  durationMinutes: any = ''
  // private touchStartX: number = 0;
  // private touchEndX: number = 0;
  // private mouseStartX: number = 0;
  // private mouseDown: boolean = false;
  // private readonly swipeThreshold: number = 30;

  quoteId = this.route.snapshot.queryParams['quoteId']
  libraryQuote: any = null

  constructor() {
    // Set responsive default font size
    this.fullscreenBookFontSize = this.getResponsiveDefaultFontSize();

    // Add window resize listener to update font size responsively
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('resize', () => {
        // Only update to responsive default if user hasn't manually changed font size
        const currentDefault = this.getResponsiveDefaultFontSize();
        if (this.fullscreenBookFontSize === 24 || this.fullscreenBookFontSize === 18) {
          this.fullscreenBookFontSize = currentDefault;
          this.applyFullscreenFontSize();
        }
      });
    }

    effect(() => {
      if(this.profileService.name() && this.profileService.profile) {
        this.setCount();
      }
    });
  }

  // Get responsive default font size based on screen width
  getResponsiveDefaultFontSize(): number {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth <= 768) {
        return 18; // Smaller font for tablet and mobile
      }
    }
    return 24; // Default large font for desktop
  }

  // Calculate proportional line-height based on font size
  calculateProportionalLineHeight(fontSize: number): number {
    return Math.round(fontSize * 1.5); // 1.5 ratio for optimal readability
  }

  ngOnInit() {
    if(this.authService.isAuth){
      this.libraryService.getLikes(this.route.snapshot.params['code']).subscribe((res: any) => {
        this.likesContent = res[0];
        this.likesCount = res[1];
      })
      this.libraryService.getFavourites().subscribe((res: any) => {
        // Проверяем, что res это массив или объект с массивом
        if (Array.isArray(res)) {
          this.favouriteContent = res.map((e: any) => e.id);
        } else if (res && Array.isArray(res.items)) {
          this.favouriteContent = res.items.map((e: any) => e.id);
        } else {
          this.favouriteContent = [];
        }
      })
    }

    // if(this.profileService.name() && this.profileService.profile) {
    //   this.setCount();
    // } else {
    //   this.profileService.setProfile();
    // }

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((lang: any) => {
      if(this.lang === lang) {
        return;
      } else {
        this.lang = lang;
      }
      this.getDate();
    })

    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      const newBookId = params['code'];
      if (newBookId !== this.bookId) {
        // Сбрасываем данные при смене книги
        this.similar = [];
        this.similarCall = false;
        this.tab = 'О книге'; // Возвращаемся на первый таб
        this.chapter = 0; // Сбрасываем главу

        if (isPlatformBrowser(this.platformId)) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
      this.bookId = newBookId;
      this.getDate();
    })

    // Handle query parameters for tab selection
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(queryParams => {
      const tabParam = queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.tab = tabParam;

        // If switching to reading tab, load the first chapter
        if (tabParam === 'Читать') {
          if (this.quoteId) {
            this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
              this.libraryQuote = res;
              this.chapter = res.page;
              this.getChapter(res.page);
            })
          } else {
            this.getChapter(0)
          }
        }
      }
    })
  }

  get duration() {
    if(!this.data || !this.data.duration) return 0
    const [hours, minutes, seconds] = this.data.duration.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + Math.round(seconds / 60);
    const formattedHours = Math.floor(totalMinutes / 60);
    const formattedMinutes = totalMinutes % 60;
    return `${formattedHours} ч. ${formattedMinutes} мин.`;
  }

  setCount() {
    this.likesContent = this.profileService.profile!.libraryLikes;
    this.favouriteContent = this.profileService.profile!.libraryFavourites;
  }

  getDate(views = true) {
    if(this.bookId)
    this.libraryService.get(this.bookId, views).subscribe((res: any) => {
      this.id = res.id
      this.titleService.setTitle(res.seo_title);
      this.metaService.updateTag({name: 'description', content: res.seo_description})
      this.data = res;

      // Безопасный расчет времени аудио
      if (this.data.audio && Array.isArray(this.data.audio) && this.data.audio.length > 0) {
        const totalSeconds = this.data.audio.reduce((a: number, b: any) => {
          const duration = Number(b.duration);
          return a + (isNaN(duration) ? 0 : duration);
        }, 0);
        this.durationMinutes = Math.ceil(totalSeconds / 60);
      } else {
        this.durationMinutes = 0;
      }

      // Обновляем массив табов в зависимости от наличия аудио
      this.updateTabs();

      // Handle tab selection from query parameters after tabs are updated
      const tabParam = this.route.snapshot.queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.changeTab(tabParam);
      } else if(this.quoteId) {
        this.changeTab('Читать');
      }

      // this.data.audio.forEach((el: any) => {
      //   this.getAudioDuration(el.url).then(duration => {
      //     el.time = this.formatDuration(duration)
      //   })
      //   .catch(error => console.error(error));
      // });
    })
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return [
      hours > 0 ? String(hours).padStart(2, "0") : "00",
      String(minutes).padStart(2, "0"),
      String(secs).padStart(2, "0")
    ].join(":");
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/library/${content.code}`).then(() =>
        this.toasterService.showToast('Ссылка на книгу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }


  favorite(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(id).subscribe({
      next: (r) => {
        if(this.favouriteContent.includes(id)) {
          this.favouriteContent.splice(this.favouriteContent.indexOf(id), 1)
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.favouriteContent.push(id)
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  like(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(id).subscribe({
      next: (r) => {

        if(this.likesContent.includes(id)) {
          this.likesContent.splice(this.likesContent.indexOf(id), 1)
          this.likesCount--;
          this.toasterService.showToast('Книга удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        } else {
          this.likesContent.push(id)
          this.likesCount++;
          this.toasterService.showToast('Книга добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        }

      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  get likes () {
    return this.likesContent.filter((item: any) => item.id === this.data.id);
  }

  changeFontSize(e: Event) {
    const target = e.target as HTMLInputElement;
    if (this.rendition) {
      // Create a style rule that targets all text elements
      const fontRule = `* { font-size: ${target.value}px !important; }`;

      // Register and apply new theme with our font size
      this.rendition.themes.register('custom-size', {
        body: {
          'font-size': `${target.value}px !important`,
        },
        'p, div, span, h1, h2, h3, h4, h5, h6': {
          'font-size': `${target.value}px !important`,
        }
      });

      // Add direct stylesheet injection
      const doc = this.rendition.getContents()[0].document;
      let style = doc.createElement('style');
      style.innerHTML = fontRule;
      doc.head.appendChild(style);

      // Select our custom theme
      this.rendition.themes.select('custom-size');
    }
  }

  changeTheme(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.select(target.value)
    this.rendition.display((document.querySelectorAll('.library-content select option')[1] as HTMLOptionElement).getAttribute('ref'))
    this.rendition.display((document.querySelectorAll('.library-content select option')[0] as HTMLOptionElement).getAttribute('ref'))
  }

  changeFont(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.override("font-family", target.value);
    //this.rendition.themes.font(`${target.value}!important`)
  }

  showQuoteContextMenu(e: MouseEvent) {
    // const selection = window.parent.document.querySelector('iframe')!.contentWindow!.getSelection
    // const offsetTop = (window.parent.document.getElementById('epub')!.offsetParent as HTMLElement).offsetTop + 93 + e.pageY;
    // (document.querySelector('.library-context') as HTMLDivElement).style.left = `${e.screenX}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.top = `${offsetTop}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.display = `block`
    // if(selection()) {
    //   this.selection.set(selection()!.toString())
    //   this.quote = selection()!.toString()
    // }
    return false
  }

  addQuoteToFavourites() {
    if(!this.id || !this.quote) return
    this.hideContextMenu()
    this.libraryService.addQuoteToFavourites(this.id, this.quote, this.currentPage).subscribe((res: any) => {
      this.quote = null
      navigator.clipboard.writeText(environment.baseUrl + '/ru/library/' + this.code + '?quoteId=' + res).then(() => {
        this.hideContextMenu()
      })
    })
  }

  hideContextMenu() {
    (document.querySelector('.library-context') as HTMLDivElement).style.display = `none`
  }

  copySelection(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      navigator.clipboard.writeText(this.selection()).then(() => {
        alert('Copied to clipboard')
        this.hideContextMenu()
      })
    }
  }

  shareQuote(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      this.addQuoteToFavourites()
    }
  }

  getTags() {
    return this.data.tags.map((e: any) => e.name).join(', ');
  }

  changeTab(tab: string) {
    this.tab = tab;

    if(tab == 'Читать') {
      if(this.quoteId) {
        this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
          this.libraryQuote = res;
          this.chapter = res.page;
          this.getChapter(res.page);
        })
      } else {
        this.getChapter(0)
      }
    }
  }

  getChapter(index: number) {
    this.chapter = index; // Обновляем текущую главу
    this.libraryService.getChapter(this.data.id, index).subscribe((res: any) => {
      this.chapterContent = res.content.content;
      this.chapterCount = res.count;
      this.chapterTitles = res.titles;

      // Рассчитываем время чтения для текущей главы
      this.chapterReadingTime = this.calculateReadingTime(this.chapterContent);

      if(this.libraryQuote && this.libraryQuote.page == index) {
        this.chapterContent = this.chapterContent.replace(this.libraryQuote.quote.trim(), `<span class="quote" style="background: yellow; color: black;">${this.libraryQuote.quote.trim()}</span>`)

        setTimeout(() => {
          const element: HTMLElement = document.querySelector('.quote')!;
          if(element) {
            window.scrollTo({
              top: element.offsetTop + 100,
              behavior: 'smooth'
            });
          }
        }, 500)

      }

    })
  }

  // Метод для расчета времени чтения
  calculateReadingTime(htmlContent: string): number {
    if (!htmlContent) return 0;

    // Удаляем HTML теги и получаем чистый текст
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Считаем слова
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;

    // Средняя скорость чтения 225 слов в минуту
    const wordsPerMinute = 225;
    return Math.ceil(words / wordsPerMinute);
  }

  getAudioDuration(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(url);
      audio.addEventListener("loadedmetadata", () => {
        resolve(audio.duration);
      });
      audio.addEventListener("error", (e) => {
        reject(`Error loading audio: ${e}`);
      });
    });
  }

  play(items: any) {
    const format = items.map(({ chapter, url, time }: ChapterData) => ({
      link: url,
      title: chapter,
      time
    }));
    this.shareDataService.changePlaylist([...format])
  }

  transform(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  prev() {
    if (this.chapter > 0) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTop();
    }
  }

  next() {
    if (this.chapter < this.chapterCount - 1) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTop();
    }
  }

  scrollToTop() {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });

    }
  }

  showFullAnnotation: boolean = false;

  toggleAnnotationDisplay() {
    this.showFullAnnotation = !this.showFullAnnotation;
  }

  isInFavourites(id: number) {
    return this.favouriteContent?.includes(id)
  }

  isLiked(id: number) {
    return this.likesContent?.includes(id)
  }

  showBookElement(elementType: string): void {
    this.relatedBooksShowLast = elementType === 'last';
    console.log(this.relatedBooksShowLast, 'this.relatedBooksShowLast');

  }

  openUrl(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link);
    }
  }

  openBook(slug: string, event?: MouseEvent) {
    // Если передан event, проверяем на Ctrl+Click для открытия в новой вкладке
    if (event) {
      if (event.ctrlKey || event.metaKey || event.button === 1 || event.shiftKey) {
        event.preventDefault();
        event.stopPropagation();
        const url = `/${this.translocoService.getActiveLang()}/library/${slug}`;
        if (isPlatformBrowser(this.platformId)) {
          window.open(url, '_blank');
        }
        return;
      }

      // Для обычного клика предотвращаем стандартное поведение
      event.preventDefault();
      event.stopPropagation();
    }

    // SPA навигация с прокруткой вверх и обновлением данных
    this.router.navigate([`${this.translocoService.getActiveLang()}/library/${slug}`]).then((success) => {
      if (success && isPlatformBrowser(this.platformId)) {
        // Прокручиваем к началу страницы
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // Сбрасываем данные похожих книг для обновления
        this.similar = [];
        this.similarCall = false;
      }
    });
  }

  selectChapter(index: any) {
    this.chapter = index;
  }

  secondsToHMS(seconds: any) {
    const numSeconds = Number(seconds);

    // Проверяем на валидность числа
    if (isNaN(numSeconds) || numSeconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const secs = Math.floor(numSeconds % 60);

    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
  }

  // Метод для обновления массива табов в зависимости от наличия аудио
  updateTabs() {
    const baseTabs = ['О книге', 'Читать'];
    if (this.hasAudio) {
      this.tabs = [...baseTabs, 'Аудиокнига'];
    } else {
      this.tabs = baseTabs;
      // Если текущий таб - Аудиокнига, переключаемся на О книге
      if (this.tab === 'Аудиокнига') {
        this.tab = 'О книге';
      }
    }
  }

  // Геттер для проверки наличия аудио
  get hasAudio(): boolean {
    return this.data &&
           this.data.audio &&
           Array.isArray(this.data.audio) &&
           this.data.audio.length > 0;
  }

  navigateToTaggedLibrary(tagId: number) {
    const lang = this.translocoService.getActiveLang();
    this.router.navigate([`/${lang}/library`], {
      queryParams: { tags: tagId }
    });
  }

  playList(items: Track[]) {
    this.shareDataService.changePlaylist([...items])
  }

  // Add to existing class properties
  @ViewChild('fullscreenDialog') fullscreenDialog!: ElementRef<HTMLDialogElement>;

  // Add this method to open the dialog
  openFullscreenDialog() {
    if (isPlatformBrowser(this.platformId)) {
      this.isFullscreenBookDialogActive = true;
      this.fullscreenDialog.nativeElement.showModal();



      // Ensure we have chapter content
      if (!this.chapterContent) {
        this.getChapter(this.chapter);
      }

    }
  }

  // Add this method to close the dialog
  closeFullscreenDialog() {
    this.isFullscreenBookDialogActive = false;
    this.fullscreenDialog.nativeElement.close();
  }











  showScrollTop: boolean = false;
  similarCall: boolean = false;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (isPlatformBrowser(this.platformId)) {
      this.showScrollTop = window.scrollY > 300;
      if(this.showScrollTop){

          if(this.similar.length === 0 && !this.similarCall) {
            this.similarCall = true;
            this.libraryService.getSimilar(this.bookId).subscribe((res: any) => {
              this.similar = res;
            });
          }
      }
    }
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -260,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 260,
        behavior: 'smooth'
      });
    }
  }

  // Navigation methods for fullscreen mode
  nextFullscreen() {
    if (this.chapter < this.chapterCount - 1) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTopFullscreen();
    }
  }

  prevFullscreen() {
    if (this.chapter > 0) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTopFullscreen();
    }
  }

  goToChapterFullscreen(chapterIndex: number) {
    this.chapterContent = ''; // Clear content to show loading
    this.getChapter(chapterIndex);
    this.fullscreenContents = false; // Close contents after selection
    this.scrollToTopFullscreen();
  }

  scrollToTopFullscreen() {
    if (isPlatformBrowser(this.platformId)) {
      // Scroll to top of the fullscreen dialog content
      const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
      if (dialogContent) {
        dialogContent.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (isPlatformBrowser(this.platformId)) {
      const target = event.target as HTMLElement;
      const settingsContainer = document.querySelector('.settings-container');

      // Если клик вне settings-container, закрываем меню
      if (settingsContainer && !settingsContainer.contains(target)) {
        this.showFullscreenOptions = false;
      }
    }
  }

  // Метод для получения стилей размера шрифта
  getFullscreenFontSizeStyles() {
    const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);
    return {
      'font-size': this.fullscreenBookFontSize + 'px',
      'line-height': lineHeight + 'px',
      '--fullscreen-font-size': this.fullscreenBookFontSize + 'px',
      '--fullscreen-line-height': lineHeight + 'px'
    };
  }

  // Метод для получения стилей ширины текста
  getFullscreenTextWidthStyles() {
    const widthMap = {
      'narrow': '700px',
      'medium': '930px',
      'full': '100%'
    };
    return {
      'max-width': widthMap[this.fullscreenBookTextWidth as keyof typeof widthMap],
      'margin': '0 auto'
    };
  }

  // Метод для применения стилей через JavaScript
  applyFullscreenFontSize() {
    if (isPlatformBrowser(this.platformId)) {
      setTimeout(() => {
        const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);

        // Находим все элементы текста внутри text-interaction
        const textElements = document.querySelectorAll('.fullscreen-book-dialog text-interaction *');

        textElements.forEach((element: any) => {
          // Сохраняем текущий font-weight перед изменением размера
          const currentFontWeight = window.getComputedStyle(element).fontWeight;
          element.style.fontSize = this.fullscreenBookFontSize + 'px';
          element.style.lineHeight = lineHeight + 'px';
          element.style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          element.style.setProperty('line-height', lineHeight + 'px', 'important');
          // Восстанавливаем font-weight
          if (currentFontWeight && currentFontWeight !== 'normal') {
            element.style.setProperty('font-weight', currentFontWeight, 'important');
          }
        });

        // Также применяем к самому text-interaction
        const textInteraction = document.querySelector('.fullscreen-book-dialog text-interaction');
        if (textInteraction) {
          const currentFontWeight = window.getComputedStyle(textInteraction).fontWeight;
          (textInteraction as HTMLElement).style.fontSize = this.fullscreenBookFontSize + 'px';
          (textInteraction as HTMLElement).style.lineHeight = lineHeight + 'px';
          (textInteraction as HTMLElement).style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          (textInteraction as HTMLElement).style.setProperty('line-height', lineHeight + 'px', 'important');
          if (currentFontWeight && currentFontWeight !== 'normal') {
            (textInteraction as HTMLElement).style.setProperty('font-weight', currentFontWeight, 'important');
          }
        }
      }, 500);
    }
  }


}

interface ChapterData {
  chapter: string;
  url: string;
  time: number
}
